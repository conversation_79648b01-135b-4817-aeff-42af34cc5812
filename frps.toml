[common]
# 常规配置
# TCP 端口
bind_port = 7000
# UDP 端口号
#bind_udp_port = 7001
# 最大连接池数量
max_pool_count = 100
# 每个客户端允许的最大端口数量，0 表示不限制
max_ports_per_client = 0
# 是否启用 TCP 多路复用
tcp_mux = true
# 允许的端口范围
allow_ports = 7000-9999,10000-65535
# 日志级别，info 表示记录信息级别的日志
log_level = info
# 日志最大保存天数
log_max_days = 1
# 日志文件路径
log_file = /frp/log/frps.log

# frp 仪表板信息
# 仪表板监听地址
dashboard_addr = "127.0.0.1"
# 仪表板监听端口
dashboard_port = 7500
# 仪表板用户名
dashboard_user = "admin"
# 仪表板密码
dashboard_pwd = "admin"

# 启用token文件认证
token_file = "./frps-tokens.toml"

#加载frps-panel插件
[[httpPlugins]]
# HTTP 插件配置
# 插件名称
name = "frps-panel"
# 插件地址
addr = "127.0.0.1:7200"
# 插件路径
path = "/handler"
# 操作列表
ops = ["Login", "NewWorkConn", "NewUserConn", "NewProxy", "Ping"]

#加载frps-tokens管理插件
[[httpPlugins]]
# Tokens管理插件配置
# 插件名称
name = "frps-tokens"
# 插件地址
addr = "127.0.0.1:7300"
# 插件路径
path = "/api/handler"
# 操作列表 - tokens管理相关操作
ops = ["Login", "NewWorkConn", "NewUserConn", "NewProxy", "Ping"]