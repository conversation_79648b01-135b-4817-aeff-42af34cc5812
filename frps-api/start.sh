#!/bin/bash

# FRP Tokens Plugin 启动脚本

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_version"
}

# 安装依赖
install_deps() {
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    log_info "安装Python依赖..."
    pip3 install -r requirements.txt
    
    if [ $? -eq 0 ]; then
        log_info "依赖安装完成"
    else
        log_error "依赖安装失败"
        exit 1
    fi
}

# 启动插件
start_plugin() {
    log_info "启动 FRP Tokens Plugin..."
    
    # 设置环境变量
    export HOST="${HOST:-127.0.0.1}"
    export PORT="${PORT:-7300}"
    export FRPS_TOKENS_PATH="${FRPS_TOKENS_PATH:-./frps-tokens.toml}"
    export DEBUG="${DEBUG:-False}"
    
    log_info "配置信息:"
    log_info "  监听地址: $HOST:$PORT"
    log_info "  配置文件: $FRPS_TOKENS_PATH"
    log_info "  调试模式: $DEBUG"
    
    # 启动插件
    python3 plugin.py
}

# 停止插件
stop_plugin() {
    log_info "停止 FRP Tokens Plugin..."
    
    local pids=$(pgrep -f "python3.*plugin.py")
    if [ -n "$pids" ]; then
        echo "$pids" | xargs kill
        log_info "插件已停止"
    else
        log_warn "未找到运行中的插件"
    fi
}

# 重启插件
restart_plugin() {
    stop_plugin
    sleep 2
    start_plugin
}

# 查看插件状态
status_plugin() {
    local port="${PORT:-7300}"
    
    # 检查进程
    local pids=$(pgrep -f "python3.*plugin.py")
    if [ -n "$pids" ]; then
        log_info "插件正在运行:"
        ps -p $pids -o pid,ppid,cmd
    else
        log_warn "插件未运行"
    fi
    
    # 检查端口占用
    if command -v netstat &> /dev/null; then
        local port_status=$(netstat -tlnp 2>/dev/null | grep ":$port ")
        if [ -n "$port_status" ]; then
            log_info "端口 $port 正在使用:"
            echo "$port_status"
        else
            log_warn "端口 $port 未被占用"
        fi
    fi
}

# 测试插件API
test_api() {
    local host="${HOST:-127.0.0.1}"
    local port="${PORT:-7300}"
    local url="http://$host:$port"
    
    log_info "测试插件API..."
    
    # 测试健康检查
    if command -v curl &> /dev/null; then
        log_info "测试健康检查接口:"
        curl -s "$url/health" | python3 -m json.tool
        
        echo ""
        log_info "测试用户列表接口:"
        curl -s "$url/api/users" | python3 -m json.tool

        echo ""
        log_info "测试服务器状态接口:"
        curl -s "$url/api/server/status" | python3 -m json.tool

        echo ""
        log_info "测试frps状态接口:"
        curl -s "$url/api/frps/status" | python3 -m json.tool
    else
        log_warn "curl 未安装，无法测试API"
    fi
}

# 显示帮助信息
show_help() {
    echo "FRP Tokens Plugin 管理脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|install|test|help}"
    echo ""
    echo "命令:"
    echo "  start    - 启动插件"
    echo "  stop     - 停止插件"
    echo "  restart  - 重启插件"
    echo "  status   - 查看插件状态"
    echo "  install  - 安装依赖"
    echo "  test     - 测试API接口"
    echo "  help     - 显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  HOST                - 监听地址 (默认: 127.0.0.1)"
    echo "  PORT                - 监听端口 (默认: 7300)"
    echo "  FRPS_TOKENS_PATH    - 配置文件路径 (默认: ./frps-tokens.toml)"
    echo "  DEBUG               - 调试模式 (默认: False)"
}

# 主函数
main() {
    case "$1" in
        start)
            check_python
            start_plugin
            ;;
        stop)
            stop_plugin
            ;;
        restart)
            restart_plugin
            ;;
        status)
            status_plugin
            ;;
        install)
            check_python
            install_deps
            ;;
        test)
            test_api
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
