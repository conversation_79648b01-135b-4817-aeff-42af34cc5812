# FRP Tokens Plugin

这是一个用于管理frps服务器tokens配置的插件，提供HTTP API接口用于与管理面板通信。

## 功能特性

- **配置管理**: 读取、写入、验证 `frps-tokens.toml` 配置文件
- **热更新**: 监听配置文件变更，自动触发重载
- **状态监控**: 监控frps服务运行状态
- **API接口**: 提供RESTful API用于远程管理
- **自动备份**: 配置文件修改前自动创建备份

## 安装部署

### 1. 环境要求

- Python 3.7+
- pip3

### 2. 安装依赖

```bash
# 进入插件目录
cd frps-api

# 安装依赖
./start.sh install
```

### 3. 配置环境变量

```bash
# 可选的环境变量配置
export HOST="127.0.0.1"              # 监听地址
export PORT="7300"                    # 监听端口
export FRPS_TOKENS_PATH="./frps-tokens.toml"  # 配置文件路径
export DEBUG="False"                  # 调试模式
```

### 4. 启动插件

```bash
# 启动插件
./start.sh start

# 查看状态
./start.sh status

# 测试API
./start.sh test
```

## API接口

### 基础接口

#### 健康检查
```
GET /health
```

#### 获取frps状态
```
GET /api/status
```

### 用户管理接口

#### 获取所有用户
```
GET /api/users
```

#### 获取单个用户
```
GET /api/users/{username}
```

#### 创建用户
```
POST /api/users
Content-Type: application/json

{
    "user": "test_user",
    "token": "your_token_here",
    "comment": "测试用户",
    "ports": ["7000-7999"],
    "domains": [""],
    "subdomains": [""],
    "enable": true
}
```

#### 更新用户
```
PUT /api/users/{username}
Content-Type: application/json

{
    "token": "new_token_here",
    "comment": "更新后的备注",
    "ports": ["8000-8999"],
    "enable": false
}
```

#### 删除用户
```
DELETE /api/users/{username}
```

#### 手动重载配置
```
POST /api/reload
```

## 响应格式

### 成功响应
```json
{
    "success": true,
    "data": {...},
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误信息"
}
```

## 配置文件格式

`frps-tokens.toml` 文件格式示例：

```toml
[tokens]
    [tokens.user1]
        user = "user1"
        token = "token123"
        comment = "用户1"
        ports = ["7000-7999"]
        domains = [""]
        subdomains = [""]
        enable = true
        
    [tokens.user2]
        user = "user2"
        token = "token456"
        comment = "用户2"
        ports = ["8000-8999"]
        domains = ["example.com"]
        subdomains = ["api"]
        enable = false
```

## 管理脚本

使用 `start.sh` 脚本管理插件：

```bash
# 启动插件
./start.sh start

# 停止插件
./start.sh stop

# 重启插件
./start.sh restart

# 查看状态
./start.sh status

# 安装依赖
./start.sh install

# 测试API
./start.sh test

# 显示帮助
./start.sh help
```

## 日志说明

插件运行时会输出以下类型的日志：

- `[INFO]` - 信息日志
- `[WARN]` - 警告日志  
- `[ERROR]` - 错误日志

## 注意事项

1. 插件默认监听 `127.0.0.1:7300`，确保端口未被占用
2. 配置文件修改前会自动创建备份到 `.backups` 目录
3. 插件会自动监听配置文件变更并触发热重载
4. 建议在生产环境中设置 `DEBUG=False`

## 故障排除

### 插件无法启动
1. 检查Python环境和依赖是否正确安装
2. 检查端口是否被占用
3. 检查配置文件路径是否正确

### API请求失败
1. 确认插件正在运行
2. 检查请求URL和端口
3. 查看插件日志输出

### 配置文件无法保存
1. 检查文件权限
2. 确认磁盘空间充足
3. 检查配置文件格式是否正确
