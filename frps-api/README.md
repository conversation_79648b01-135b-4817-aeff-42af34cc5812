# FRP Tokens Plugin

这是一个专门用于管理frps-tokens.toml配置文件的插件，提供HTTP API接口用于与控制中心面板通信。

## 功能特性

- **精确配置管理**: 支持对 `frps-tokens.toml` 文件中的具体参数进行增删改操作
- **热更新通知**: 监听配置文件变更，提供热重载通知
- **API接口**: 提供RESTful API用于与控制中心面板通信
- **被动更新**: 只响应面板请求，不主动更新配置

## 安装部署

### 1. 环境要求

- Python 3.7+
- pip3
- frps服务器

### 2. 启动插件

插件独立运行，不需要在frps.toml中配置。

### 3. 安装依赖

```bash
# 进入插件目录
cd frps-api

# 安装依赖
./start.sh install
```

### 4. 配置环境变量

```bash
# 可选的环境变量配置
export HOST="127.0.0.1"              # 监听地址
export PORT="7300"                    # 监听端口
export FRPS_TOKENS_PATH="./frps-tokens.toml"  # 配置文件路径
export DEBUG="False"                  # 调试模式
```

### 5. 启动插件

```bash
# 启动插件
./start.sh start

# 查看状态
./start.sh status

# 测试API
./start.sh test
```

## API接口

### 基础接口

#### 健康检查
```
GET /health
```

#### 获取服务器状态
```
GET /api/server/status
```

返回服务器的CPU、内存、磁盘、网络等状态信息。

#### 获取frps服务状态
```
GET /api/frps/status
```

返回frps进程的运行状态、资源使用情况等信息。

### 用户管理接口

#### 获取所有用户
```
GET /api/users
```

#### 获取单个用户
```
GET /api/users/{username}
```

#### 创建用户
```
POST /api/users
Content-Type: application/json

{
    "user": "test_user",
    "token": "your_token_here",
    "comment": "测试用户",
    "ports": ["7000-7999"],
    "domains": [""],
    "subdomains": [""],
    "enable": true
}
```

#### 更新用户
```
PUT /api/users/{username}
Content-Type: application/json

{
    "token": "new_token_here",
    "comment": "更新后的备注",
    "ports": ["8000-8999"],
    "enable": false
}
```

#### 删除用户
```
DELETE /api/users/{username}
```

#### 手动重载配置
```
POST /api/reload
```

### 精确字段管理接口

#### 更新用户单个字段
```
PUT /api/users/{username}/field/{field}
Content-Type: application/json

{
    "value": "new_value"
}
```

支持的字段: `token`, `comment`, `ports`, `domains`, `subdomains`, `enable`

### 端口管理接口

#### 添加端口
```
POST /api/users/{username}/ports
Content-Type: application/json

{
    "port": "8000-8999"
}
```

#### 删除端口
```
DELETE /api/users/{username}/ports/{port}
```

### 域名管理接口

#### 添加域名
```
POST /api/users/{username}/domains
Content-Type: application/json

{
    "domain": "example.com"
}
```

#### 删除域名
```
DELETE /api/users/{username}/domains/{domain}
```

### 子域名管理接口

#### 添加子域名
```
POST /api/users/{username}/subdomains
Content-Type: application/json

{
    "subdomain": "api"
}
```

#### 删除子域名
```
DELETE /api/users/{username}/subdomains/{subdomain}
```

## 响应格式

### 成功响应
```json
{
    "success": true,
    "data": {...},
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误信息"
}
```

## 配置文件格式

`frps-tokens.toml` 文件格式示例：

```toml
[tokens]
    [tokens.user1]
        user = "user1"
        token = "token123"
        comment = "用户1"
        ports = ["7000-7999"]
        domains = [""]
        subdomains = [""]
        enable = true
        
    [tokens.user2]
        user = "user2"
        token = "token456"
        comment = "用户2"
        ports = ["8000-8999"]
        domains = ["example.com"]
        subdomains = ["api"]
        enable = false
```

## 管理脚本

使用 `start.sh` 脚本管理插件：

```bash
# 启动插件
./start.sh start

# 停止插件
./start.sh stop

# 重启插件
./start.sh restart

# 查看状态
./start.sh status

# 安装依赖
./start.sh install

# 测试API
./start.sh test

# 显示帮助
./start.sh help
```

## 日志说明

插件运行时会输出以下类型的日志：

- `[INFO]` - 信息日志
- `[WARN]` - 警告日志  
- `[ERROR]` - 错误日志

## 注意事项

1. 插件默认监听 `127.0.0.1:7300`，确保端口未被占用
2. 配置文件修改前会自动创建备份到 `.backups` 目录
3. 插件会自动监听配置文件变更并触发热重载
4. 建议在生产环境中设置 `DEBUG=False`

## 故障排除

### 插件无法启动
1. 检查Python环境和依赖是否正确安装
2. 检查端口是否被占用
3. 检查配置文件路径是否正确

### API请求失败
1. 确认插件正在运行
2. 检查请求URL和端口
3. 查看插件日志输出

### 配置文件无法保存
1. 检查文件权限
2. 确认磁盘空间充足
3. 检查配置文件格式是否正确
