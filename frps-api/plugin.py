#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FRP Tokens Plugin
frps服务器的tokens配置管理插件
只提供API接口，用于与管理面板通信
"""

import os
import json
import time
import psutil
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import toml
from flask import Flask, request, jsonify
from flask_cors import CORS
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变更监听器"""
    
    def __init__(self, plugin):
        self.plugin = plugin
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path == self.plugin.config_path:
            print(f"[INFO] 配置文件变更: {event.src_path}")
            self.plugin.on_config_changed()

class FRPSTokensPlugin:
    """FRPS Tokens管理插件"""
    
    def __init__(self, config_path: str = "./frps-tokens.toml"):
        self.config_path = os.path.abspath(config_path)
        self.backup_dir = os.path.join(os.path.dirname(self.config_path), ".backups")
        self.observer = None
        self.status_cache = {}
        self.last_status_update = None
        
        # 确保备份目录存在
        Path(self.backup_dir).mkdir(exist_ok=True)
        
        # 启动文件监听
        self.start_file_watcher()
        
        print(f"[INFO] FRP Tokens Plugin 初始化完成")
        print(f"[INFO] 配置文件: {self.config_path}")
        print(f"[INFO] 备份目录: {self.backup_dir}")
    
    def start_file_watcher(self):
        """启动文件变更监听"""
        try:
            self.observer = Observer()
            event_handler = ConfigFileHandler(self)
            watch_dir = os.path.dirname(self.config_path)
            self.observer.schedule(event_handler, watch_dir, recursive=False)
            self.observer.start()
            print(f"[INFO] 开始监听配置文件变更")
        except Exception as e:
            print(f"[ERROR] 启动文件监听失败: {e}")
    
    def stop_file_watcher(self):
        """停止文件监听"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            print(f"[INFO] 文件监听已停止")
    
    def on_config_changed(self):
        """配置文件变更回调"""
        print(f"[INFO] 配置文件已变更，触发热重载")
        # 这里可以添加通知frps重新加载配置的逻辑
        # 例如发送信号、调用frps API等
    
    def create_backup(self) -> str:
        """创建配置文件备份"""
        if not os.path.exists(self.config_path):
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"frps-tokens.toml.{timestamp}"
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            print(f"[INFO] 配置备份已创建: {backup_filename}")
            return backup_path
        except Exception as e:
            print(f"[ERROR] 创建备份失败: {e}")
            return ""
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                # 创建默认配置文件
                default_config = {"tokens": {}}
                self.save_config(default_config)
                return default_config
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = toml.load(f)
            
            if 'tokens' not in config:
                config['tokens'] = {}
            
            return config
        except Exception as e:
            raise Exception(f"加载配置文件失败: {e}")
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            # 创建备份
            self.create_backup()
            
            # 保存新配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                toml.dump(config, f)
            
            print(f"[INFO] 配置文件已保存")
            return True
        except Exception as e:
            print(f"[ERROR] 保存配置文件失败: {e}")
            return False
    
    def get_frps_status(self) -> Dict[str, Any]:
        """获取frps服务状态"""
        now = datetime.now()
        
        # 缓存状态信息1秒
        if (self.last_status_update and 
            (now - self.last_status_update).total_seconds() < 1 and 
            self.status_cache):
            return self.status_cache
        
        status = {
            "timestamp": now.isoformat(),
            "running": False,
            "pid": None,
            "cpu_percent": 0.0,
            "memory_mb": 0.0,
            "uptime_seconds": 0,
            "connections": 0
        }
        
        # 查找frps进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'frps' in proc.info['name'] or any('frps' in cmd for cmd in proc.info['cmdline']):
                    status.update({
                        "running": True,
                        "pid": proc.pid,
                        "cpu_percent": proc.cpu_percent(),
                        "memory_mb": proc.memory_info().rss / 1024 / 1024,
                        "uptime_seconds": int(time.time() - proc.create_time()),
                        "connections": len(proc.connections()) if hasattr(proc, 'connections') else 0
                    })
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        self.status_cache = status
        self.last_status_update = now
        return status
    
    def get_all_users(self) -> Dict[str, Any]:
        """获取所有用户配置"""
        config = self.load_config()
        return config.get('tokens', {})
    
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """获取单个用户配置"""
        users = self.get_all_users()
        return users.get(username)
    
    def create_user(self, username: str, user_config: Dict[str, Any]) -> bool:
        """创建用户"""
        config = self.load_config()
        
        if username in config['tokens']:
            raise ValueError(f"用户 {username} 已存在")
        
        # 设置默认值
        user_config.setdefault('user', username)
        user_config.setdefault('enable', True)
        user_config.setdefault('comment', '')
        user_config.setdefault('ports', [])
        user_config.setdefault('domains', [])
        user_config.setdefault('subdomains', [])
        
        config['tokens'][username] = user_config
        return self.save_config(config)
    
    def update_user(self, username: str, user_config: Dict[str, Any]) -> bool:
        """更新用户配置"""
        config = self.load_config()
        
        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")
        
        user_config['user'] = username  # 确保用户名一致
        config['tokens'][username] = user_config
        return self.save_config(config)
    
    def delete_user(self, username: str) -> bool:
        """删除用户"""
        config = self.load_config()
        
        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")
        
        del config['tokens'][username]
        return self.save_config(config)

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 全局插件实例
plugin = None

def init_plugin():
    """初始化插件"""
    global plugin
    config_path = os.getenv('FRPS_TOKENS_PATH', './frps-tokens.toml')
    plugin = FRPSTokensPlugin(config_path)

# FRP HTTP插件协议处理
@app.route('/api/handler', methods=['POST'])
def frp_plugin_handler():
    """处理frps的HTTP插件请求"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'reject': True, 'reject_reason': '无效的请求数据'})

        op = data.get('op', '')
        content = data.get('content', {})

        print(f"[INFO] 收到frps插件请求: op={op}")

        # 根据操作类型处理请求
        if op == 'Login':
            # 处理用户登录验证
            return handle_login(content)
        elif op == 'NewWorkConn':
            # 处理新工作连接
            return handle_new_work_conn(content)
        elif op == 'NewUserConn':
            # 处理新用户连接
            return handle_new_user_conn(content)
        elif op == 'NewProxy':
            # 处理新代理
            return handle_new_proxy(content)
        elif op == 'Ping':
            # 处理心跳
            return handle_ping(content)
        else:
            print(f"[WARN] 未知操作类型: {op}")
            return jsonify({'reject': False})

    except Exception as e:
        print(f"[ERROR] 处理frps插件请求失败: {e}")
        return jsonify({'reject': True, 'reject_reason': str(e)})

def handle_login(content):
    """处理用户登录验证"""
    try:
        user = content.get('user', '')
        token = content.get('token', '')

        print(f"[INFO] 用户登录验证: user={user}")

        # 从配置文件中验证用户
        users = plugin.get_all_users()
        user_config = users.get(user)

        if not user_config:
            print(f"[WARN] 用户不存在: {user}")
            return jsonify({'reject': True, 'reject_reason': '用户不存在'})

        if not user_config.get('enable', False):
            print(f"[WARN] 用户已禁用: {user}")
            return jsonify({'reject': True, 'reject_reason': '用户已禁用'})

        if user_config.get('token', '') != token:
            print(f"[WARN] Token验证失败: {user}")
            return jsonify({'reject': True, 'reject_reason': 'Token验证失败'})

        print(f"[INFO] 用户登录成功: {user}")
        return jsonify({'reject': False})

    except Exception as e:
        print(f"[ERROR] 处理登录验证失败: {e}")
        return jsonify({'reject': True, 'reject_reason': str(e)})

def handle_new_work_conn(content):
    """处理新工作连接"""
    print(f"[INFO] 新工作连接: {content}")
    return jsonify({'reject': False})

def handle_new_user_conn(content):
    """处理新用户连接"""
    print(f"[INFO] 新用户连接: {content}")
    return jsonify({'reject': False})

def handle_new_proxy(content):
    """处理新代理"""
    try:
        user = content.get('user', '')
        proxy_name = content.get('proxy_name', '')
        proxy_type = content.get('proxy_type', '')
        remote_port = content.get('remote_port', 0)

        print(f"[INFO] 新代理请求: user={user}, proxy={proxy_name}, type={proxy_type}, port={remote_port}")

        # 验证用户权限
        users = plugin.get_all_users()
        user_config = users.get(user)

        if not user_config:
            return jsonify({'reject': True, 'reject_reason': '用户不存在'})

        if not user_config.get('enable', False):
            return jsonify({'reject': True, 'reject_reason': '用户已禁用'})

        # 检查端口权限
        allowed_ports = user_config.get('ports', [])
        if allowed_ports and remote_port:
            port_allowed = False
            for port_range in allowed_ports:
                if '-' in port_range:
                    start_port, end_port = map(int, port_range.split('-'))
                    if start_port <= remote_port <= end_port:
                        port_allowed = True
                        break
                elif str(remote_port) == port_range:
                    port_allowed = True
                    break

            if not port_allowed:
                print(f"[WARN] 端口权限不足: user={user}, port={remote_port}")
                return jsonify({'reject': True, 'reject_reason': f'端口 {remote_port} 不在允许范围内'})

        print(f"[INFO] 代理创建成功: user={user}, proxy={proxy_name}")
        return jsonify({'reject': False})

    except Exception as e:
        print(f"[ERROR] 处理新代理失败: {e}")
        return jsonify({'reject': True, 'reject_reason': str(e)})

def handle_ping(content):
    """处理心跳"""
    return jsonify({'reject': False})

# API路由
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'plugin': 'frps-tokens',
        'version': '1.0.0'
    })

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取frps服务状态"""
    try:
        status = plugin.get_frps_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users', methods=['GET'])
def get_users():
    """获取所有用户"""
    try:
        users = plugin.get_all_users()
        return jsonify({
            'success': True,
            'data': users,
            'count': len(users)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>', methods=['GET'])
def get_user(username):
    """获取单个用户"""
    try:
        user = plugin.get_user(username)
        if user is None:
            return jsonify({
                'success': False,
                'error': '用户不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': user
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users', methods=['POST'])
def create_user():
    """创建用户"""
    try:
        data = request.get_json()
        if not data or 'user' not in data:
            return jsonify({
                'success': False,
                'error': '缺少用户名'
            }), 400
        
        username = data['user']
        plugin.create_user(username, data)
        
        return jsonify({
            'success': True,
            'message': '用户创建成功',
            'data': data
        }), 201
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 409
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>', methods=['PUT'])
def update_user(username):
    """更新用户"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少请求数据'
            }), 400
        
        plugin.update_user(username, data)
        
        return jsonify({
            'success': True,
            'message': '用户更新成功',
            'data': data
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>', methods=['DELETE'])
def delete_user(username):
    """删除用户"""
    try:
        plugin.delete_user(username)
        
        return jsonify({
            'success': True,
            'message': '用户删除成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reload', methods=['POST'])
def reload_config():
    """手动重载配置"""
    try:
        plugin.on_config_changed()
        return jsonify({
            'success': True,
            'message': '配置重载成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # 初始化插件
    init_plugin()
    
    # 获取配置
    host = os.getenv('HOST', '127.0.0.1')
    port = int(os.getenv('PORT', '7300'))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    print(f"[INFO] FRP Tokens Plugin 启动中...")
    print(f"[INFO] 监听地址: {host}:{port}")
    print(f"[INFO] 配置文件: {plugin.config_path}")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print(f"\n[INFO] 正在关闭插件...")
    finally:
        if plugin:
            plugin.stop_file_watcher()
        print(f"[INFO] 插件已关闭")
