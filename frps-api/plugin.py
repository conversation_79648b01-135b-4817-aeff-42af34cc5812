#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FRP Tokens Plugin
frps服务器的tokens配置管理插件
专注于frps-tokens.toml文件的增删改和与控制中心面板通信
"""

import os
import json
import time
import psutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import toml
from flask import Flask, request, jsonify
from flask_cors import CORS
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变更监听器"""
    
    def __init__(self, plugin):
        self.plugin = plugin
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path == self.plugin.config_path:
            print(f"[INFO] 配置文件变更: {event.src_path}")
            self.plugin.on_config_changed()

class FRPSTokensPlugin:
    """FRPS Tokens管理插件 - 专注于配置文件精确管理"""

    def __init__(self, config_path: str = "./frps-tokens.toml"):
        self.config_path = os.path.abspath(config_path)
        self.observer = None
        self.status_cache = {}
        self.last_status_update = None

        # 启动文件监听（仅用于热更新通知）
        self.start_file_watcher()

        print(f"[INFO] FRP Tokens Plugin 初始化完成")
        print(f"[INFO] 配置文件: {self.config_path}")
    
    def start_file_watcher(self):
        """启动文件变更监听"""
        try:
            self.observer = Observer()
            event_handler = ConfigFileHandler(self)
            watch_dir = os.path.dirname(self.config_path)
            self.observer.schedule(event_handler, watch_dir, recursive=False)
            self.observer.start()
            print(f"[INFO] 开始监听配置文件变更")
        except Exception as e:
            print(f"[ERROR] 启动文件监听失败: {e}")
    
    def stop_file_watcher(self):
        """停止文件监听"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            print(f"[INFO] 文件监听已停止")
    
    def on_config_changed(self):
        """配置文件变更回调 - 仅用于热更新通知"""
        print(f"[INFO] 配置文件已变更，触发热重载通知")
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                # 创建默认配置文件
                default_config = {"tokens": {}}
                self.save_config(default_config)
                return default_config
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = toml.load(f)
            
            if 'tokens' not in config:
                config['tokens'] = {}
            
            return config
        except Exception as e:
            raise Exception(f"加载配置文件失败: {e}")
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            # 直接保存配置，不创建备份
            with open(self.config_path, 'w', encoding='utf-8') as f:
                toml.dump(config, f)

            print(f"[INFO] 配置文件已保存")
            return True
        except Exception as e:
            print(f"[ERROR] 保存配置文件失败: {e}")
            return False
    

    
    def get_all_users(self) -> Dict[str, Any]:
        """获取所有用户配置"""
        config = self.load_config()
        return config.get('tokens', {})
    
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """获取单个用户配置"""
        users = self.get_all_users()
        return users.get(username)
    
    def create_user(self, username: str, user_config: Dict[str, Any]) -> bool:
        """创建用户"""
        config = self.load_config()
        
        if username in config['tokens']:
            raise ValueError(f"用户 {username} 已存在")
        
        # 设置默认值
        user_config.setdefault('user', username)
        user_config.setdefault('enable', True)
        user_config.setdefault('comment', '')
        user_config.setdefault('ports', [])
        user_config.setdefault('domains', [])
        user_config.setdefault('subdomains', [])
        
        config['tokens'][username] = user_config
        return self.save_config(config)
    
    def update_user(self, username: str, user_config: Dict[str, Any]) -> bool:
        """更新用户配置"""
        config = self.load_config()
        
        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")
        
        user_config['user'] = username  # 确保用户名一致
        config['tokens'][username] = user_config
        return self.save_config(config)
    
    def delete_user(self, username: str) -> bool:
        """删除用户"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        del config['tokens'][username]
        return self.save_config(config)

    def update_user_field(self, username: str, field: str, value: Any) -> bool:
        """更新用户的单个字段"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        # 验证字段名
        valid_fields = ['token', 'comment', 'ports', 'domains', 'subdomains', 'enable']
        if field not in valid_fields:
            raise ValueError(f"无效的字段名: {field}")

        config['tokens'][username][field] = value
        return self.save_config(config)

    def add_user_port(self, username: str, port: str) -> bool:
        """为用户添加端口"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        ports = config['tokens'][username].get('ports', [])
        if port not in ports:
            ports.append(port)
            config['tokens'][username]['ports'] = ports
            return self.save_config(config)
        return True

    def remove_user_port(self, username: str, port: str) -> bool:
        """移除用户的端口"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        ports = config['tokens'][username].get('ports', [])
        if port in ports:
            ports.remove(port)
            config['tokens'][username]['ports'] = ports
            return self.save_config(config)
        return True

    def add_user_domain(self, username: str, domain: str) -> bool:
        """为用户添加域名"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        domains = config['tokens'][username].get('domains', [])
        if domain not in domains:
            domains.append(domain)
            config['tokens'][username]['domains'] = domains
            return self.save_config(config)
        return True

    def remove_user_domain(self, username: str, domain: str) -> bool:
        """移除用户的域名"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        domains = config['tokens'][username].get('domains', [])
        if domain in domains:
            domains.remove(domain)
            config['tokens'][username]['domains'] = domains
            return self.save_config(config)
        return True

    def add_user_subdomain(self, username: str, subdomain: str) -> bool:
        """为用户添加子域名"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        subdomains = config['tokens'][username].get('subdomains', [])
        if subdomain not in subdomains:
            subdomains.append(subdomain)
            config['tokens'][username]['subdomains'] = subdomains
            return self.save_config(config)
        return True

    def remove_user_subdomain(self, username: str, subdomain: str) -> bool:
        """移除用户的子域名"""
        config = self.load_config()

        if username not in config['tokens']:
            raise ValueError(f"用户 {username} 不存在")

        subdomains = config['tokens'][username].get('subdomains', [])
        if subdomain in subdomains:
            subdomains.remove(subdomain)
            config['tokens'][username]['subdomains'] = subdomains
            return self.save_config(config)
        return True

    def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        try:
            # 获取系统信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # 获取网络信息
            network = psutil.net_io_counters()

            # 获取系统启动时间
            boot_time = psutil.boot_time()
            uptime_seconds = int(time.time() - boot_time)

            return {
                "timestamp": datetime.now().isoformat(),
                "status": "online",
                "cpu_percent": cpu_percent,
                "memory": {
                    "total_mb": round(memory.total / 1024 / 1024, 2),
                    "used_mb": round(memory.used / 1024 / 1024, 2),
                    "available_mb": round(memory.available / 1024 / 1024, 2),
                    "percent": memory.percent
                },
                "disk": {
                    "total_gb": round(disk.total / 1024 / 1024 / 1024, 2),
                    "used_gb": round(disk.used / 1024 / 1024 / 1024, 2),
                    "free_gb": round(disk.free / 1024 / 1024 / 1024, 2),
                    "percent": round((disk.used / disk.total) * 100, 2)
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "uptime_seconds": uptime_seconds
            }
        except Exception as e:
            return {
                "timestamp": datetime.now().isoformat(),
                "status": "error",
                "error": str(e)
            }

    def get_frps_status(self) -> Dict[str, Any]:
        """获取frps服务状态"""
        now = datetime.now()

        # 缓存状态信息1秒
        if (self.last_status_update and
            (now - self.last_status_update).total_seconds() < 1 and
            self.status_cache):
            return self.status_cache

        status = {
            "timestamp": now.isoformat(),
            "running": False,
            "pid": None,
            "cpu_percent": 0.0,
            "memory_mb": 0.0,
            "uptime_seconds": 0,
            "connections": 0,
            "start_time": None
        }

        try:
            # 查找frps进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    # 检查进程名或命令行参数中是否包含frps
                    if ('frps' in proc.info['name'] or
                        any('frps' in str(cmd) for cmd in proc.info.get('cmdline', []))):

                        # 获取进程详细信息
                        process = psutil.Process(proc.info['pid'])
                        create_time = proc.info['create_time']

                        status.update({
                            "running": True,
                            "pid": proc.info['pid'],
                            "cpu_percent": process.cpu_percent(),
                            "memory_mb": round(process.memory_info().rss / 1024 / 1024, 2),
                            "uptime_seconds": int(time.time() - create_time),
                            "start_time": datetime.fromtimestamp(create_time).isoformat(),
                            "connections": len(process.connections()) if hasattr(process, 'connections') else 0
                        })
                        break
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

        except Exception as e:
            status["error"] = str(e)

        self.status_cache = status
        self.last_status_update = now
        return status

# 创建Flask应用
app = Flask(__name__)
CORS(app)

# 全局插件实例
plugin = None

def init_plugin():
    """初始化插件"""
    global plugin
    config_path = os.getenv('FRPS_TOKENS_PATH', './frps-tokens.toml')
    plugin = FRPSTokensPlugin(config_path)



# API路由
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.now().isoformat(),
        'plugin': 'frps-tokens',
        'version': '1.0.0'
    })

@app.route('/api/server/status', methods=['GET'])
def get_server_status():
    """获取服务器状态"""
    try:
        status = plugin.get_server_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/frps/status', methods=['GET'])
def get_frps_status():
    """获取frps服务状态"""
    try:
        status = plugin.get_frps_status()
        return jsonify({
            'success': True,
            'data': status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500



@app.route('/api/users', methods=['GET'])
def get_users():
    """获取所有用户"""
    try:
        users = plugin.get_all_users()
        return jsonify({
            'success': True,
            'data': users,
            'count': len(users)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>', methods=['GET'])
def get_user(username):
    """获取单个用户"""
    try:
        user = plugin.get_user(username)
        if user is None:
            return jsonify({
                'success': False,
                'error': '用户不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': user
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users', methods=['POST'])
def create_user():
    """创建用户"""
    try:
        data = request.get_json()
        if not data or 'user' not in data:
            return jsonify({
                'success': False,
                'error': '缺少用户名'
            }), 400
        
        username = data['user']
        plugin.create_user(username, data)
        
        return jsonify({
            'success': True,
            'message': '用户创建成功',
            'data': data
        }), 201
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 409
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>', methods=['PUT'])
def update_user(username):
    """更新用户"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少请求数据'
            }), 400
        
        plugin.update_user(username, data)
        
        return jsonify({
            'success': True,
            'message': '用户更新成功',
            'data': data
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>', methods=['DELETE'])
def delete_user(username):
    """删除用户"""
    try:
        plugin.delete_user(username)
        
        return jsonify({
            'success': True,
            'message': '用户删除成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reload', methods=['POST'])
def reload_config():
    """手动重载配置"""
    try:
        plugin.on_config_changed()
        return jsonify({
            'success': True,
            'message': '配置重载成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 精确字段管理API
@app.route('/api/users/<username>/field/<field>', methods=['PUT'])
def update_user_field(username, field):
    """更新用户的单个字段"""
    try:
        data = request.get_json()
        if not data or 'value' not in data:
            return jsonify({
                'success': False,
                'error': '缺少value参数'
            }), 400

        plugin.update_user_field(username, field, data['value'])

        return jsonify({
            'success': True,
            'message': f'用户 {username} 的 {field} 字段更新成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 端口管理API
@app.route('/api/users/<username>/ports', methods=['POST'])
def add_user_port(username):
    """为用户添加端口"""
    try:
        data = request.get_json()
        if not data or 'port' not in data:
            return jsonify({
                'success': False,
                'error': '缺少port参数'
            }), 400

        plugin.add_user_port(username, data['port'])

        return jsonify({
            'success': True,
            'message': f'端口 {data["port"]} 添加成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>/ports/<port>', methods=['DELETE'])
def remove_user_port(username, port):
    """移除用户的端口"""
    try:
        plugin.remove_user_port(username, port)

        return jsonify({
            'success': True,
            'message': f'端口 {port} 移除成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 域名管理API
@app.route('/api/users/<username>/domains', methods=['POST'])
def add_user_domain(username):
    """为用户添加域名"""
    try:
        data = request.get_json()
        if not data or 'domain' not in data:
            return jsonify({
                'success': False,
                'error': '缺少domain参数'
            }), 400

        plugin.add_user_domain(username, data['domain'])

        return jsonify({
            'success': True,
            'message': f'域名 {data["domain"]} 添加成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>/domains/<domain>', methods=['DELETE'])
def remove_user_domain(username, domain):
    """移除用户的域名"""
    try:
        plugin.remove_user_domain(username, domain)

        return jsonify({
            'success': True,
            'message': f'域名 {domain} 移除成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 子域名管理API
@app.route('/api/users/<username>/subdomains', methods=['POST'])
def add_user_subdomain(username):
    """为用户添加子域名"""
    try:
        data = request.get_json()
        if not data or 'subdomain' not in data:
            return jsonify({
                'success': False,
                'error': '缺少subdomain参数'
            }), 400

        plugin.add_user_subdomain(username, data['subdomain'])

        return jsonify({
            'success': True,
            'message': f'子域名 {data["subdomain"]} 添加成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<username>/subdomains/<subdomain>', methods=['DELETE'])
def remove_user_subdomain(username, subdomain):
    """移除用户的子域名"""
    try:
        plugin.remove_user_subdomain(username, subdomain)

        return jsonify({
            'success': True,
            'message': f'子域名 {subdomain} 移除成功'
        })
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # 初始化插件
    init_plugin()
    
    # 获取配置
    host = os.getenv('HOST', '127.0.0.1')
    port = int(os.getenv('PORT', '7300'))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    print(f"[INFO] FRP Tokens Plugin 启动中...")
    print(f"[INFO] 监听地址: {host}:{port}")
    print(f"[INFO] 配置文件: {plugin.config_path}")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print(f"\n[INFO] 正在关闭插件...")
    finally:
        if plugin:
            plugin.stop_file_watcher()
        print(f"[INFO] 插件已关闭")
